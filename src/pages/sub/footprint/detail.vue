<template>
  <view class="page-container">
    <!-- 导航栏 -->
    <u-navbar class="navbar" bgColor="#ffffff" leftIcon="arrow-left" :placeholder="true" @leftClick="$back" style="z-index: 50">
      <template #center>
        <text class="nav-title">评价详情</text>
      </template>
    </u-navbar>

    <!-- 内容区域 -->
    <mescroll-uni class="scroll-container" :fixed="false" :down="downOpt1" :up="upOpt3" @down="downCallback" @init="mescrollInit" @up="upCallback">
      <view class="detail-container" v-if="detail">
        <!-- 剧目信息卡片 -->
        <view class="show-card">
          <view class="show-header">
            <view class="show-info">
              <text class="show-name">{{ detail.repertoireName || detail.theaterName }}</text>
              <text class="show-venue" v-if="detail.theaterName && detail.repertoireName">{{ detail.theaterName }}</text>
              <text class="show-date" v-if="detail.showTime">{{ detail.showTime }}</text>
            </view>
            <!-- <view class="show-type-tag">
              <text class="show-type">{{ detail.showType || '话剧' }}</text>
            </view> -->
          </view>

          <!-- 封面图片 -->
          <view class="image-container" v-if="detail.coverPicture">
            <u-image class="cover-image" :src="$picFormat(detail.coverPicture)" mode="aspectFill" width="100%" height="400rpx" bgColor="transparent"></u-image>

            <!-- 精品标签 -->
            <view class="image-overlay" v-if="detail.isPremium">
              <view class="premium-tag">
                <text class="fas fa-crown premium-icon"></text>
                <text class="premium-text">精品</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 用户评价内容 -->
        <view class="content-card">
          <view class="user-section">
            <view class="user-info">
              <view class="user-avatar">
                <u-avatar :defaultUrl="$iconFormat('avatar.jpg')" :src="detail.userAvatar" size="60rpx"></u-avatar>
              </view>
              <view class="user-details">
                <text class="user-name">{{ detail.userName }}</text>
                <text class="create-time">{{ detail.createTime }}</text>
              </view>
            </view>
          </view>

          <!-- 评价内容 -->
          <view class="content-section" v-if="detail.content">
            <u-parse class="content-text" :content="detail.content" :copyLink="false" :previewImg="true" :showImgMenu="true" :setTitle="false"></u-parse>
          </view>

          <!-- 互动数据 -->
          <view class="interaction-section">
            <view class="interaction-stats">
              <view class="stat-item" @tap="handleCollectAction">
                <text class="fas fa-heart stat-icon collect-icon" :class="{ active: isFollowed }"></text>
                <text class="stat-count">{{ detail.followCount || 0 }}</text>
                <text class="stat-label">关注</text>
              </view>
              <view class="stat-item" @tap="handleShareAction">
                <text class="fas fa-share stat-icon share-icon"></text>
                <text class="stat-count">{{ detail.shareCount || 0 }}</text>
                <text class="stat-label">转发</text>
              </view>
              <view class="stat-item" @tap="handleLikeAction">
                <text class="fas fa-thumbs-up stat-icon like-icon" :class="{ active: detail.isLiked }"></text>
                <text class="stat-count">{{ detail.likeCount || 0 }}</text>
                <text class="stat-label">赞同</text>
              </view>
              <view class="stat-item" @tap="openCommentInput">
                <text class="fas fa-comment stat-icon comment-icon"></text>
                <text class="stat-count">{{ commentsList.length || 0 }}</text>
                <text class="stat-label">留言</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 用户留言列表 -->
        <view class="comments-section">
          <view class="section-title">
            <text class="title-text">用户留言</text>
            <text class="comment-count">({{ commentsList.length }})</text>
          </view>

          <!-- 留言列表 -->
          <view class="comments-list" v-if="commentsList && commentsList.length > 0">
            <view class="comment-item" v-for="(comment, index) in commentsList" :key="comment.id">
              <!-- 用户信息和操作区 -->
              <view class="comment-header">
                <view class="comment-user">
                  <view class="comment-avatar">
                    <u-avatar :defaultUrl="$iconFormat('avatar.jpg')" :src="comment.userAvatar" size="48rpx"></u-avatar>
                  </view>
                  <view class="comment-user-info">
                    <text class="comment-username">{{ comment.userName }}</text>
                    <text class="comment-time">{{ comment.createTime }} 来自{{ comment.location || '北京' }}</text>
                  </view>
                </view>

                <!-- 留言操作 -->
                <view class="comment-actions">
                  <view class="comment-action" @tap="handleCommentLike(comment, index)">
                    <text class="fas fa-thumbs-up comment-action-icon" :class="{ liked: comment.isLiked }"></text>
                    <text class="comment-action-text">{{ comment.likeCount || 0 }}</text>
                  </view>
                  <view class="comment-action" @tap="handleReplyComment(comment)">
                    <text class="fas fa-reply comment-action-icon"></text>
                    <text class="comment-action-text">回复</text>
                  </view>
                </view>
              </view>

              <!-- 留言内容 -->
              <view class="comment-content">
                <!-- 文字内容 -->
                <u-parse class="comment-text" :content="comment.content" :copyLink="false" :previewImg="true" :showImgMenu="true" :setTitle="false" v-if="comment.content"></u-parse>

                <!-- 图片内容 -->
                <view class="comment-images" v-if="comment.images && comment.images.length > 0">
                  <view class="comment-image-item" v-for="(image, imgIndex) in comment.images" :key="imgIndex" @tap="previewImage(comment.images, imgIndex)">
                    <u-image :src="$picFormat(image)" mode="aspectFill" width="120rpx" height="120rpx" bgColor="#F5F5F5" radius="8rpx"></u-image>
                  </view>
                </view>
              </view>

              <!-- 二级回复列表 -->
              <view class="replies-list" v-if="comment.replies && comment.replies.length > 0">
                <view class="reply-item" v-for="(reply, replyIndex) in comment.replies" :key="reply.id">
                  <!-- 回复用户信息和操作区 -->
                  <view class="reply-header">
                    <view class="reply-user">
                      <view class="reply-avatar">
                        <u-avatar :defaultUrl="$iconFormat('avatar.jpg')" :src="reply.userAvatar" size="36rpx"></u-avatar>
                      </view>
                      <view class="reply-user-info">
                        <text class="reply-username">{{ reply.userName }}</text>
                        <text class="reply-time">{{ reply.createTime }} 来自{{ reply.location || '上海' }}</text>
                      </view>
                    </view>

                    <!-- 回复操作 -->
                    <view class="reply-actions">
                      <view class="reply-action" @tap="handleReplyLike(reply, index, replyIndex)">
                        <text class="fas fa-thumbs-up reply-action-icon" :class="{ liked: reply.isLiked }"></text>
                        <text class="reply-action-text">{{ reply.likeCount || 0 }}</text>
                      </view>
                      <view class="reply-action" @tap="handleReplyToReply(comment, reply)">
                        <text class="fas fa-reply reply-action-icon"></text>
                        <text class="reply-action-text">回复</text>
                      </view>
                    </view>
                  </view>

                  <!-- 回复内容 -->
                  <view class="reply-content">
                    <text class="reply-target" v-if="reply.replyToUser">回复 @{{ reply.replyToUser }}：</text>
                    <u-parse class="reply-text" :content="reply.content" :copyLink="false" :previewImg="true" :showImgMenu="true" :setTitle="false"></u-parse>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="comments-empty" v-else>
            <text class="fas fa-comments empty-icon"></text>
            <text class="empty-text">还没有留言，快来抢沙发吧~</text>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-container" v-else>
        <u-loading-icon mode="flower"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>
    </mescroll-uni>

    <!-- 发表留言输入框 -->
    <view class="comment-input-section" v-if="detail && showCommentInput">
      <view class="comment-input-container">
        <view class="input-header">
          <text class="input-title">发表留言</text>
          <text class="fas fa-times close-icon" @tap="closeCommentInput"></text>
        </view>

        <view class="input-content">
          <!-- 回复目标提示 -->
          <view class="reply-target-hint" v-if="replyTarget">
            <text class="reply-hint-text">回复 @{{ replyTarget.userName }}</text>
            <text class="fas fa-times cancel-reply" @tap="cancelReply"></text>
          </view>

          <!-- 文字输入 -->
          <textarea class="comment-textarea" v-model="commentText" placeholder="说说你的想法..." :maxlength="500" :auto-height="true" :show-confirm-bar="false"></textarea>

          <!-- 图片选择 -->
          <view class="image-upload-section">
            <view class="uploaded-images" v-if="uploadedImages.length > 0">
              <view class="uploaded-image-item" v-for="(image, index) in uploadedImages" :key="index">
                <u-image :src="image" mode="aspectFill" width="120rpx" height="120rpx" bgColor="#F5F5F5" radius="8rpx"></u-image>
                <view class="delete-image" @tap="removeImage(index)">
                  <text class="fas fa-times"></text>
                </view>
              </view>
            </view>

            <!-- 暂时隐藏添加图片功能，日后再开放 -->
            <!-- <view class="add-image-btn" v-if="uploadedImages.length < 9" @tap="chooseImage">
              <text class="fas fa-plus add-icon"></text>
              <text class="add-text">添加图片</text>
            </view> -->
          </view>

          <!-- 操作按钮 -->
          <view class="input-actions">
            <view class="action-btn cancel-btn" @tap="closeCommentInput">
              <text class="action-text">取消</text>
            </view>
            <view class="action-btn submit-btn" :class="{ disabled: !canSubmit }" @tap="submitComment">
              <text class="action-text">发表</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部留言输入提示框 -->
    <view class="bottom-comment-input" v-if="detail" @tap="openCommentInput">
      <view class="comment-input-hint">
        <text class="input-placeholder">说说你的想法...</text>
        <view class="input-actions-hint">
          <text class="fas fa-image image-icon"></text>
          <text class="fas fa-edit edit-icon"></text>
        </view>
      </view>
    </view>

    <!-- 网络状态 -->
    <network />
  </view>
</template>

<script lang="ts" setup>
import network from '@/components/Network.vue'
import { useGlobalStore } from '@/stores/global'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import { $back, $iconFormat, $picFormat } from '@/utils/methods'
import dayjs from 'dayjs'

// API将直接使用uni.$u.http调用

const globalStore = useGlobalStore()
const { downOpt1, upOpt3, userInfo } = storeToRefs(globalStore)
const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

// 页面数据
const id = ref<number>()
const detail = ref<any>(null)
const commentsList = ref<any[]>([])

// 用户操作状态
const isFollowed = ref(false) // 本地关注状态

// 留言输入相关
const showCommentInput = ref(false)
const commentText = ref('')
const uploadedImages = ref<string[]>([])
const replyTarget = ref<any>(null) // 回复目标（一级或二级留言）

// 计算属性
const canSubmit = computed(() => {
  return commentText.value.trim().length > 0 || uploadedImages.value.length > 0
})

onLoad((params: any) => {
  id.value = Number(params.id)
})

// 数据加载 - 使用新的评论详情接口
const upCallback = (mescroll: any) => {
  if (!id.value) {
    mescroll.endErr()
    return
  }

  // 调用新的评论详情接口
  uni.$u.http
    .get('/comment/getCommentDetail', {
      params: {
        commentId: id.value,
        userId: userInfo.value?.id
      }
    })
    .then((res: any) => {
      if (res.code === 200 && res.data) {
        const data = res.data

        // 处理用户操作状态

        // 确保状态字段存在，如果API没有返回则设置默认值
        if (data.isLiked === undefined || data.isLiked === null) {
          data.isLiked = null // 默认未点赞状态
        }

        // 初始化本地关注状态（由于API不返回关注状态，我们假设未关注）
        isFollowed.value = false
        console.log('页面初始化，关注状态:', isFollowed.value)

        // 处理图片链接
        if (data.userAvatar) data.userAvatar = $picFormat(data.userAvatar)
        if (data.theaterCoverPicture) data.theaterCoverPicture = $picFormat(data.theaterCoverPicture)
        if (data.repertoireCoverPicture) data.repertoireCoverPicture = $picFormat(data.repertoireCoverPicture)
        if (data.commentImage) data.commentImage = $picFormat(data.commentImage)

        // 处理时间格式
        if (data.createTime) data.createTime = dayjs(data.createTime).format('YYYY年MM月DD日 HH:mm')
        if (data.sessionName) {
          // sessionName 格式为 "2024-01-15 19:30-21:30"，转换为显示格式
          data.showTime = data.sessionName
        }

        // 设置封面图片优先级：剧目封面图 > 评论图片 > 剧场封面图
        data.coverPicture = data.repertoireCoverPicture || data.commentImage || data.theaterCoverPicture

        // 设置显示类型
        data.showType = '评论'

        // 处理回复列表
        if (data.replyCommentList && data.replyCommentList.length > 0) {
          data.replyCommentList.forEach((reply: any) => {
            if (reply.userAvatar) reply.userAvatar = $picFormat(reply.userAvatar)
            if (reply.replyAvatar) reply.replyAvatar = $picFormat(reply.replyAvatar)
            if (reply.createTime) reply.createTime = dayjs(reply.createTime).format('YYYY年MM月DD日 HH:mm')
          })
          commentsList.value = data.replyCommentList
        } else {
          commentsList.value = []
        }

        detail.value = data
        mescroll.endSuccess(1, false)
      } else {
        console.error('获取评论详情失败:', res.msg || '未知错误')
        mescroll.endErr()
      }
    })
    .catch(error => {
      console.error('加载评论详情失败:', error)
      mescroll.endErr()
    })
}

// 留言列表数据现在从API接口获取
/* 已移除模拟留言生成函数
const generateCommentsList = () => {
  const mockComments = [
    {
      id: 1,
      userName: '戏剧小白',
      userAvatar: 'https://picsum.photos/100/100?random=201',
      content: '看完这个评价我也想去看这部剧了！楼主写得太好了，特别是对演员表演的描述，让我仿佛身临其境。',
      createTime: '2小时前',
      location: '广东',
      likeCount: 12,
      isLiked: false,
      images: [],
      replies: [
        {
          id: 11,
          userName: '楼主',
          userAvatar: 'https://picsum.photos/100/100?random=211',
          content: '谢谢支持！希望你也能去现场感受一下',
          createTime: '1小时前',
          location: '北京',
          likeCount: 5,
          isLiked: false,
          replyToUser: '戏剧小白'
        },
        {
          id: 12,
          userName: '路人甲',
          userAvatar: 'https://picsum.photos/100/100?random=212',
          content: '我也想去，有人一起吗？',
          createTime: '30分钟前',
          location: '广东',
          likeCount: 2,
          isLiked: true,
          replyToUser: '戏剧小白'
        }
      ]
    },
    {
      id: 2,
      userName: '文艺青年小李',
      userAvatar: 'https://picsum.photos/100/100?random=202',
      content: '同感！我上个月也看了，确实很震撼。',
      createTime: '1小时前',
      location: '上海',
      likeCount: 8,
      isLiked: true,
      images: ['https://picsum.photos/300/300?random=301', 'https://picsum.photos/300/300?random=302']
    },
    {
      id: 3,
      userName: '剧院常客老王',
      userAvatar: 'https://picsum.photos/100/100?random=203',
      content: '这个剧院的音响效果真的很棒，每次去都有不同的感受。推荐大家选择中间的位置，视野和音效都是最佳的。',
      createTime: '30分钟前',
      location: '天津',
      likeCount: 15,
      isLiked: false,
      images: []
    },
    {
      id: 4,
      userName: '音乐剧爱好者',
      userAvatar: 'https://picsum.photos/100/100?random=204',
      content: '楼主的评价很专业！我也是戏剧爱好者，看了很多场演出。',
      createTime: '15分钟前',
      location: '江苏',
      likeCount: 6,
      isLiked: false,
      images: ['https://picsum.photos/300/300?random=303']
    },
    {
      id: 5,
      userName: '新手观众',
      userAvatar: 'https://picsum.photos/100/100?random=205',
      content: '第一次看话剧，选择这部真的太对了！感谢楼主的推荐，已经开始期待下一部了。',
      createTime: '5分钟前',
      location: '浙江',
      likeCount: 3,
      isLiked: true,
      images: []
    }
  ]

  commentsList.value = mockComments
}
*/

// 互动操作方法
const handleLikeAction = () => {
  if (!detail.value || !userInfo.value?.id) return

  const newStatus = detail.value.isLiked === 1 ? null : 1
  const requestData = {
    commentId: detail.value.id,
    userId: userInfo.value.id,
    type: newStatus // 1: 赞, 0: 踩, null: 取消
  }

  // 调用点赞API
  uni.$u.http
    .post('/commentInfo/save', requestData)
    .then((res: any) => {
      if (res.code === 200) {
        // 更新本地状态
        detail.value.isLiked = newStatus
        if (newStatus === 1) {
          detail.value.likeCount = (detail.value.likeCount || 0) + 1
          uni.showToast({ title: '赞同成功', icon: 'success' })
        } else {
          detail.value.likeCount = Math.max(0, (detail.value.likeCount || 0) - 1)
          uni.showToast({ title: '取消赞同', icon: 'none' })
        }
      } else {
        uni.showToast({ title: res.msg || '操作失败', icon: 'none' })
      }
    })
    .catch(error => {
      console.error('点赞操作失败:', error)
      uni.showToast({ title: '操作失败', icon: 'none' })
    })
}

const handleShareAction = () => {
  if (!detail.value || !userInfo.value?.id) return

  // 微信转发功能
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: `pages/sub/footprint/detail?id=${detail.value.id}`,
    title: `${detail.value.repertoireName || detail.value.theaterName} - 评价分享`,
    summary: detail.value.content ? detail.value.content.replace(/<[^>]+>/g, '').substring(0, 100) : '来看看这个精彩的评价吧！',
    imageUrl: detail.value.coverPicture || '',
    success: () => {
      // 记录转发
      const requestData = {
        commentId: detail.value.id,
        userId: userInfo.value.id,
        type: 2 // 2: 转发
      }

      uni.$u.http
        .post('/commentInfo/save', requestData)
        .then((res: any) => {
          if (res.code === 200) {
            detail.value.shareCount = (detail.value.shareCount || 0) + 1
            uni.showToast({ title: '转发成功', icon: 'success' })
          }
        })
        .catch(error => {
          console.error('记录转发失败:', error)
        })
    },
    fail: err => {
      console.error('转发失败:', err)
      uni.showToast({ title: '转发失败', icon: 'none' })
    }
  })
}

const handleCollectAction = () => {
  if (!detail.value || !userInfo.value?.id) return

  console.log('点击关注按钮，当前状态:', isFollowed.value)
  const newStatus = isFollowed.value ? null : 3
  console.log('将要发送的状态:', newStatus)

  const requestData = {
    commentId: detail.value.id,
    userId: userInfo.value.id,
    type: newStatus // 3: 关注, null: 取消关注
  }

  // 调用关注API
  uni.$u.http
    .post('/commentInfo/save', requestData)
    .then((res: any) => {
      if (res.code === 200) {
        // 更新本地状态
        const oldStatus = isFollowed.value
        isFollowed.value = newStatus === 3
        console.log('状态更新:', oldStatus, '->', isFollowed.value)

        if (newStatus === 3) {
          detail.value.followCount = (detail.value.followCount || 0) + 1
          uni.showToast({ title: '关注成功', icon: 'success' })
        } else {
          detail.value.followCount = Math.max(0, (detail.value.followCount || 0) - 1)
          uni.showToast({ title: '取消关注', icon: 'none' })
        }
      } else {
        uni.showToast({ title: res.msg || '操作失败', icon: 'none' })
      }
    })
    .catch(error => {
      console.error('关注操作失败:', error)
      uni.showToast({ title: '操作失败', icon: 'none' })
    })
}

// 测试函数 - 手动切换关注状态（用于调试）
const testToggleFollow = () => {
  isFollowed.value = !isFollowed.value
  console.log('手动切换关注状态:', isFollowed.value)
}

// 留言相关方法
const openCommentInput = () => {
  showCommentInput.value = true
}

const closeCommentInput = () => {
  showCommentInput.value = false
  commentText.value = ''
  uploadedImages.value = []
  replyTarget.value = null
}

// 取消回复
const cancelReply = () => {
  replyTarget.value = null
  commentText.value = ''
}

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 9 - uploadedImages.value.length,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: res => {
      uploadedImages.value.push(...res.tempFilePaths)
    },
    fail: err => {
      console.error('选择图片失败:', err)
      uni.showToast({ title: '选择图片失败', icon: 'none' })
    }
  })
}

// 删除图片
const removeImage = (index: number) => {
  uploadedImages.value.splice(index, 1)
}

// 预览图片
const previewImage = (images: string[], current: number) => {
  uni.previewImage({
    urls: images.map(img => $picFormat(img)),
    current: current
  })
}

// 提交留言
const submitComment = () => {
  if (!canSubmit.value || !userInfo.value?.id) return

  const requestData = {
    userId: userInfo.value.id,
    content: commentText.value.trim(),
    parentId: replyTarget.value ? (replyTarget.value.isReply ? replyTarget.value.parentComment?.id : replyTarget.value.id) : detail.value.id,
    commentParentId: replyTarget.value ? detail.value.id : 0,
    replyUserId: replyTarget.value ? replyTarget.value.userId : null,
    theaterId: detail.value.theaterId || null,
    repertoireId: detail.value.repertoireId || null,
    repertoireInfoDetailId: detail.value.repertoireInfoDetailId || null
  }

  // 调用回复API
  uni.$u.http
    .post('/comment/reply', requestData)
    .then((res: any) => {
      if (res.code === 200) {
        // 构建新的回复数据
        const newReply = {
          id: res.data?.id || Date.now(),
          userId: userInfo.value.id,
          userName: userInfo.value.nickName || userInfo.value.userName || '我',
          userAvatar: $picFormat(userInfo.value.avatarUrl || ''),
          content: commentText.value.trim(),
          createTime: dayjs().format('YYYY年MM月DD日 HH:mm'),
          likeCount: 0,
          shareCount: 0,
          followCount: 0,
          isLiked: null,
          replyUserId: replyTarget.value?.userId || null,
          replyName: replyTarget.value?.userName || null,
          replyAvatar: replyTarget.value?.userAvatar || null
        }

        if (replyTarget.value) {
          // 添加到回复列表
          commentsList.value.push(newReply)
          uni.showToast({ title: '回复发表成功', icon: 'success' })
        } else {
          // 添加到评论列表
          commentsList.value.unshift(newReply)
          uni.showToast({ title: '留言发表成功', icon: 'success' })
        }

        // 更新评论数量
        if (detail.value) {
          detail.value.replyCount = (detail.value.replyCount || 0) + 1
        }
      } else {
        uni.showToast({ title: res.msg || '发表失败', icon: 'none' })
      }
    })
    .catch(error => {
      console.error('发表留言失败:', error)
      uni.showToast({ title: '发表失败', icon: 'none' })
    })

  closeCommentInput()
}

// 留言点赞
const handleCommentLike = (comment: any, index: number) => {
  comment.isLiked = !comment.isLiked
  if (comment.isLiked) {
    comment.likeCount = (comment.likeCount || 0) + 1
  } else {
    comment.likeCount = Math.max(0, (comment.likeCount || 0) - 1)
  }
}

// 回复留言
const handleReplyComment = (comment: any) => {
  replyTarget.value = { ...comment, isReply: false }
  commentText.value = ''
  openCommentInput()
}

// 二级回复相关方法
const handleReplyLike = (reply: any, commentIndex: number, replyIndex: number) => {
  reply.isLiked = !reply.isLiked
  if (reply.isLiked) {
    reply.likeCount = (reply.likeCount || 0) + 1
  } else {
    reply.likeCount = Math.max(0, (reply.likeCount || 0) - 1)
  }
}

const handleReplyToReply = (comment: any, reply: any) => {
  replyTarget.value = { ...reply, isReply: true, parentComment: comment }
  commentText.value = ''
  openCommentInput()
}
</script>

<style lang="scss" scoped>
@import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';

.page-container {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.navbar {
  background: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.scroll-container {
  flex: 1;
  min-height: 0;
}

.detail-container {
  padding: 24rpx;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 剧目信息卡片 */
.show-card {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.show-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx;
  border-bottom: 1px solid #f5f5f5;
}

.show-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.show-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 40rpx;
  margin-bottom: 8rpx;
}

.show-venue {
  font-size: 26rpx;
  color: #666666;
  line-height: 32rpx;
  margin-bottom: 4rpx;
}

.show-date {
  font-size: 24rpx;
  color: #999999;
  line-height: 30rpx;
}

.show-type-tag {
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
}

.show-type {
  font-size: 22rpx;
  color: #ffffff;
  font-weight: 500;
}

/* 图片容器 */
.image-container {
  position: relative;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  border-radius: 0;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, transparent 30%);
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding: 24rpx;
}

.premium-tag {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
}

.premium-icon {
  font-size: 20rpx;
  color: #8b4513;
  margin-right: 6rpx;
}

.premium-text {
  font-size: 24rpx;
  color: #8b4513;
  font-weight: 600;
}

/* 内容卡片 */
.content-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.user-section {
  margin-bottom: 32rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  border: 2px solid #f5f5f5;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 34rpx;
  margin-bottom: 4rpx;
}

.create-time {
  font-size: 24rpx;
  color: #999999;
  line-height: 30rpx;
}

/* 内容区域 */
.content-section {
  margin-bottom: 32rpx;
}

.content-text {
  font-size: 30rpx;
  color: #333333;
  line-height: 44rpx;
  text-align: justify;
}

/* 互动数据 */
.interaction-section {
  padding-top: 24rpx;
  border-top: 1px solid #f5f5f5;
}

.interaction-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  background: #fafbfc;
  border-radius: 20rpx;
  min-width: 120rpx;
}

.stat-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  color: #9ca3af; /* 高级灰作为默认颜色 */
}

.like-icon {
  &.active {
    color: #2563eb; /* 蓝色高亮 */
  }
}

/* 转发按钮保持高级灰，不需要高亮状态 */

.collect-icon {
  &.active {
    color: #dc2626; /* 红色高亮 */
  }
}

.comment-icon {
  color: #8b5cf6;
}

.stat-count {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
}

/* 用户留言列表 */
.comments-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.comment-count {
  font-size: 24rpx;
  color: #999999;
  margin-left: 8rpx;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.comment-item {
  padding: 20rpx;
  background: #fafbfc;
  border-radius: 16rpx;
  border: 1px solid #f0f0f0;
}

/* 留言头部布局 */
.comment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.comment-user {
  display: flex;
  align-items: center;
  flex: 1;
}

.comment-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16rpx;
  border: 2px solid #f5f5f5;
}

.comment-user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.comment-username {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 32rpx;
  margin-bottom: 4rpx;
}

.comment-time {
  font-size: 22rpx;
  color: #999999;
  line-height: 28rpx;
}

.comment-content {
  margin-bottom: 16rpx;
}

.comment-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  margin-bottom: 12rpx;
}

.comment-images {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.comment-image-item {
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.comment-actions {
  display: flex;
  gap: 16rpx;
  flex-shrink: 0;
}

.comment-action {
  display: flex;
  align-items: center;
  padding: 6rpx 12rpx;
  background: #ffffff;
  border-radius: 16rpx;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;

  &:active {
    background: #f5f5f5;
    transform: scale(0.95);
  }
}

.comment-action-icon {
  font-size: 20rpx;
  color: #666666;
  margin-right: 8rpx;

  &.liked {
    color: #ff6b6b;
  }
}

.comment-action-text {
  font-size: 22rpx;
  color: #666666;
  font-weight: 500;
}

.comments-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 64rpx;
  color: #cccccc;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 26rpx;
  color: #999999;
  line-height: 36rpx;
}

/* 二级回复样式 */
.replies-list {
  margin-top: 16rpx;
  padding-left: 20rpx;
  border-left: 2px solid #f0f0f0;
}

.reply-item {
  padding: 16rpx 0;
  border-bottom: 1px solid #f8f9fa;

  &:last-child {
    border-bottom: none;
  }
}

/* 二级回复头部布局 */
.reply-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.reply-user {
  display: flex;
  align-items: center;
  flex: 1;
}

.reply-avatar {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12rpx;
  border: 1px solid #f5f5f5;
}

.reply-user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.reply-username {
  font-size: 24rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 28rpx;
}

.reply-target {
  font-size: 24rpx;
  color: #9333ea;
  line-height: 32rpx;
  margin-right: 8rpx;
  font-weight: 500;
}

.reply-time {
  font-size: 20rpx;
  color: #999999;
  line-height: 24rpx;
}

.reply-content {
  margin-bottom: 12rpx;
}

.reply-text {
  font-size: 26rpx;
  color: #333333;
  line-height: 36rpx;
}

.reply-actions {
  display: flex;
  gap: 12rpx;
  flex-shrink: 0;
}

.reply-action {
  display: flex;
  align-items: center;
  padding: 4rpx 8rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;

  &:active {
    background: #f5f5f5;
    transform: scale(0.95);
  }
}

.reply-action-icon {
  font-size: 18rpx;
  color: #666666;
  margin-right: 6rpx;

  &.liked {
    color: #45b7d1;
  }
}

.reply-action-text {
  font-size: 20rpx;
  color: #666666;
  font-weight: 500;
}

/* 底部留言输入提示框 */
.bottom-comment-input {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 16rpx 24rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  border-top: 1px solid #f0f0f0;
  z-index: 100;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.comment-input-hint {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: #fafbfc;
  border-radius: 24rpx;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;

  &:active {
    background: #f0f1f3;
    border-color: #9333ea;
  }
}

.input-placeholder {
  font-size: 28rpx;
  color: #999999;
  flex: 1;
}

.input-actions-hint {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.image-icon,
.edit-icon {
  font-size: 24rpx;
  color: #666666;
}

/* 回复目标提示 */
.reply-target-hint {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #f3e8ff 0%, #e0e7ff 100%);
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  border: 1px solid #c4b5fd;
}

.reply-hint-text {
  font-size: 26rpx;
  color: #7c3aed;
  font-weight: 500;
}

.cancel-reply {
  font-size: 20rpx;
  color: #9333ea;
  padding: 8rpx;
  border-radius: 50%;
  background: rgba(147, 51, 234, 0.1);

  &:active {
    background: rgba(147, 51, 234, 0.2);
  }
}

/* 留言输入框 */
.comment-input-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.comment-input-container {
  width: 100%;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #f0f0f0;
}

.input-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.close-icon {
  font-size: 28rpx;
  color: #999999;
  padding: 8rpx;
}

.input-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.comment-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  background: #fafbfc;
  border-radius: 16rpx;
  border: 1px solid #e0e0e0;
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  margin-bottom: 24rpx;
  resize: none;
  box-sizing: border-box;

  &:focus {
    border-color: #9333ea;
    background: #ffffff;
  }
}

.image-upload-section {
  margin-bottom: 32rpx;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.uploaded-image-item {
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.delete-image {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);

  .fas {
    font-size: 16rpx;
    color: #ffffff;
  }
}

.add-image-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  background: #fafbfc;
  border: 2px dashed #cccccc;
  border-radius: 8rpx;
  transition: all 0.3s ease;

  &:active {
    background: #f0f1f3;
    border-color: #9333ea;
  }
}

.add-icon {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 20rpx;
  color: #999999;
}

.input-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.cancel-btn {
  background: #f5f5f5;
  border: 1px solid #e0e0e0;

  .action-text {
    color: #666666;
  }
}

.submit-btn {
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);

  .action-text {
    color: #ffffff;
    font-weight: 600;
  }

  &.disabled {
    background: #cccccc;

    .action-text {
      color: #999999;
    }
  }
}

.action-text {
  font-size: 28rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999999;
  margin-top: 20rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .detail-container {
    padding: 20rpx;
  }

  .show-header {
    padding: 20rpx;
  }

  .content-card {
    padding: 24rpx;
  }

  .related-section {
    padding: 24rpx;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.show-card,
.content-card,
.related-section {
  animation: fadeInUp 0.6s ease-out;
}
</style>
